const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';
  
  return merge(baseConfig(env, options), {
    name: 'admin',
    entry: {
      main: [
        './frontend_admin/sport-wrench-admin.module.js',
        './frontend_admin/sport-wrench-admin.config.js',
        './frontend_admin/sport-wrench-admin.routes.js',
        './frontend_admin/main.controller.js',
        './frontend_admin/**/*.js'
      ]
    },
    
    output: {
      path: path.resolve(__dirname, '.tmp/public_admin'),
      filename: 'scripts/[name].js',
      publicPath: '/'
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './frontend_admin/index.html',
        filename: 'index.html',
        inject: 'body',
        chunks: ['main', 'vendor', 'vendor_bower']
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components'
          },
          {
            from: 'assets/images',
            to: 'images'
          },
          {
            from: 'assets/fonts',
            to: 'fonts'
          },
          {
            from: 'assets/js',
            to: 'js'
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico'
          },
          // Copy all HTML templates
          {
            from: 'frontend_admin/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend_admin',
            globOptions: {
              ignore: ['**/index.html']
            }
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public_admin'),
      },
      port: 3003,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
