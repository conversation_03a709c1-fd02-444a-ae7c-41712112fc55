const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');
const glob = require('glob');
const {urls} = require('./config/urls');
const paymentHubConfig = require('./config/paymentHub');

const entryFiles = [
  ...glob.sync('./frontend_admin/sport-wrench-admin.module.js'),
  ...glob.sync('./frontend_admin/**/*.module.js'),
  ...glob.sync('./frontend_admin/**/*.constant.js'),
  ...glob.sync('./frontend_admin/**/*.config.js'),
  ...glob.sync('./frontend_admin/**/*.js'),
  ...glob.sync('./assets/styles/main.scss'),
  ...glob.sync('./assets/styles/admin.scss')
];

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';

  return merge(baseConfig(env, options), {
    name: 'admin',
    entry: entryFiles,

    output: {
      filename: '[name].js',
      path: path.resolve(__dirname, '.tmp/public2'),
      publicPath: '/',
    },

    plugins: [
      new (require('webpack')).DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
        'process.env.API_URL': JSON.stringify(urls.api),
        'process.env.PAYMENT_HUB_URL': JSON.stringify(paymentHubConfig.url),
        'process.env.PAYMENT_HUB_API_KEY': JSON.stringify(paymentHubConfig.apiKey)
      }),

      new HtmlWebpackPlugin({
        template: './frontend_admin/index.html',
        filename: 'index.html',
        inject: 'body'
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components'
          },
          {
            from: 'assets/images',
            to: 'images'
          },
          {
            from: 'assets/fonts',
            to: 'fonts'
          },
          {
            from: 'assets/js',
            to: 'js'
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico'
          },
          // Copy all HTML templates
          {
            from: 'frontend_admin/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend_admin',
            globOptions: {
              ignore: ['**/index.html']
            }
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public_admin'),
      },
      port: 3003,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
