const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';
  
  return merge(baseConfig(env, options), {
    name: 'main',
    entry: {
      main: [
        './frontend/sport-wrench.module.js',
        './frontend/sport-wrench.config.js',
        './frontend/sport-wrench.constant.js',
        './frontend/sport-wrench.routes.constant.js',
        './frontend/sport-wrench.routes.js',
        './frontend/main.controller.js',
        './frontend/**/*.js'
      ]
    },
    
    output: {
      path: path.resolve(__dirname, '.tmp/public'),
      filename: 'scripts/[name].js',
      publicPath: '/'
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './assets/index.html',
        filename: 'index.html',
        inject: 'body',
        chunks: ['main', 'vendor', 'vendor_bower']
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components'
          },
          {
            from: 'assets/images',
            to: 'images'
          },
          {
            from: 'assets/fonts',
            to: 'fonts'
          },
          {
            from: 'assets/js',
            to: 'js'
          },
          {
            from: 'assets/documents',
            to: 'documents'
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico'
          },
          {
            from: 'assets/robots.txt',
            to: 'robots.txt'
          },
          {
            from: 'frontend/privacy.html',
            to: 'privacy.html'
          },
          {
            from: 'frontend/terms.html',
            to: 'terms.html'
          },
          // Copy all HTML templates
          {
            from: 'frontend/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend'
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public'),
      },
      port: 3001,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
