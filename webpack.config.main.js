const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');
const glob = require('glob');
const {urls} = require('./config/urls');
const paymentHubConfig = require('./config/paymentHub');

// Get all JS files but remove duplicates
const jsFiles = [
  './frontend/sport-wrench.module.js',
  ...glob.sync('./frontend/**/*.js')
].filter((file, index, arr) => arr.indexOf(file) === index);

const entryFiles = jsFiles;

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';

  return merge(baseConfig(env, options), {
    name: 'main',
    entry: entryFiles,

    output: {
      filename: '[name].js',
      path: path.resolve(__dirname, '.tmp/public'),
      publicPath: '/',
    },
    
    plugins: [
      new (require('webpack')).DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
        'process.env.API_URL': JSON.stringify(urls.api),
        'process.env.PAYMENT_HUB_URL': JSON.stringify(paymentHubConfig.url),
        'process.env.PAYMENT_HUB_API_KEY': JSON.stringify(paymentHubConfig.apiKey)
      }),

      new HtmlWebpackPlugin({
        template: './assets/index-webpack.html',
        filename: 'index.html',
        inject: 'body'
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components',
            noErrorOnMissing: true
          },
          {
            from: 'assets/images',
            to: 'images',
            noErrorOnMissing: true
          },
          {
            from: 'assets/js',
            to: 'js',
            noErrorOnMissing: true
          },
          {
            from: 'assets/documents',
            to: 'documents',
            noErrorOnMissing: true
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico',
            noErrorOnMissing: true
          },
          {
            from: 'assets/robots.txt',
            to: 'robots.txt',
            noErrorOnMissing: true
          },
          {
            from: 'frontend/privacy.html',
            to: 'privacy.html',
            noErrorOnMissing: true
          },
          {
            from: 'frontend/terms.html',
            to: 'terms.html',
            noErrorOnMissing: true
          },
          // Copy all HTML templates
          {
            from: 'frontend/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend',
            noErrorOnMissing: true
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public'),
      },
      port: 3001,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
