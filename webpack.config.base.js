const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';
  
  return {
    mode: options.mode || 'development',
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env'],
              cacheDirectory: true
            }
          }
        },
        {
          test: /\.html$/,
          use: 'html-loader'
        },
        {
          test: /\.scss$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader',
            'sass-loader'
          ]
        },
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader'
          ]
        },
        {
          test: /\.(png|jpg|jpeg|gif|svg|ico)$/,
          type: 'asset/resource',
          generator: {
            filename: 'images/[name][ext]'
          }
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/,
          type: 'asset/resource',
          generator: {
            filename: 'fonts/[name][ext]'
          }
        }
      ]
    },
    
    plugins: [
      new CleanWebpackPlugin(),
      ...(isProduction ? [
        new MiniCssExtractPlugin({
          filename: 'styles/[name].css'
        })
      ] : [])
    ],
    
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendor',
            chunks: 'all',
          },
          bower: {
            test: /[\\/]bower_components[\\/]/,
            name: 'vendor_bower',
            chunks: 'all',
          }
        }
      }
    },
    
    resolve: {
      extensions: ['.js', '.html', '.scss', '.css'],
      alias: {
        '@': path.resolve(__dirname, 'frontend'),
        '@event': path.resolve(__dirname, 'frontend_event'),
        '@admin': path.resolve(__dirname, 'frontend_admin'),
        '@assets': path.resolve(__dirname, 'assets')
      }
    }
  };
};
