const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');
const glob = require('glob');
const {urls} = require('./config/urls');
const paymentHubConfig = require('./config/paymentHub');

const entryFiles = [
  ...glob.sync('./frontend_event/sport-wrench.module.js'),
  ...glob.sync('./frontend_event/**/*.module.js'),
  ...glob.sync('./frontend_event/**/*.constant.js'),
  ...glob.sync('./frontend_event/**/*.config.js'),
  ...glob.sync('./frontend_event/**/*.js'),
  ...glob.sync('./assets/styles/main.scss'),
  ...glob.sync('./assets/styles/admin.scss'),
  ...glob.sync('./assets/stylesEsw/main.scss'),
  ...glob.sync('./assets/stylesTickets/test.scss')
];

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';

  return merge(baseConfig(env, options), {
    name: 'events',
    entry: entryFiles,

    output: {
      filename: '[name].js',
      path: path.resolve(__dirname, '.tmp/public2'),
      publicPath: '/',
    },

    plugins: [
      new (require('webpack')).DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
        'process.env.API_URL': JSON.stringify(urls.api),
        'process.env.PAYMENT_HUB_URL': JSON.stringify(paymentHubConfig.url),
        'process.env.PAYMENT_HUB_API_KEY': JSON.stringify(paymentHubConfig.apiKey)
      }),

      new HtmlWebpackPlugin({
        template: './frontend_event/index.html',
        filename: 'index.html',
        inject: 'body'
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components'
          },
          {
            from: 'assets/images',
            to: 'images'
          },
          {
            from: 'assets/fonts',
            to: 'fonts'
          },
          {
            from: 'assets/js',
            to: 'js'
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico'
          },
          {
            from: 'frontend_event/google62f33f95b3037e0b.html',
            to: 'google62f33f95b3037e0b.html'
          },
          {
            from: 'frontend_event/bsqprevqual.html',
            to: 'bsqprevqual.html'
          },
          {
            from: 'frontend_event/db100final.html',
            to: 'db100final.html'
          },
          // Copy all HTML templates
          {
            from: 'frontend_event/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend_event',
            globOptions: {
              ignore: ['**/index.html']
            }
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public_events'),
      },
      port: 3002,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
