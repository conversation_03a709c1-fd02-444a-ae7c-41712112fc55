const path = require('path');
const { merge } = require('webpack-merge');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const baseConfig = require('./webpack.config.base');

module.exports = (env, options) => {
  const isProduction = options.mode === 'production';
  
  return merge(baseConfig(env, options), {
    name: 'events',
    entry: {
      main: [
        './frontend_event/sport-wrench.module.js',
        './frontend_event/sport-wrench.config.js',
        './frontend_event/sport-wrench.constant.js',
        './frontend_event/sport-wrench.routes.js',
        './frontend_event/main.controller.js',
        './frontend_event/**/*.js'
      ]
    },
    
    output: {
      path: path.resolve(__dirname, '.tmp/public_events'),
      filename: 'scripts/[name].js',
      publicPath: '/'
    },
    
    plugins: [
      new HtmlWebpackPlugin({
        template: './frontend_event/index.html',
        filename: 'index.html',
        inject: 'body',
        chunks: ['main', 'vendor', 'vendor_bower']
      }),
      
      new CopyWebpackPlugin({
        patterns: [
          {
            from: 'assets/bower_components',
            to: 'bower_components'
          },
          {
            from: 'assets/images',
            to: 'images'
          },
          {
            from: 'assets/fonts',
            to: 'fonts'
          },
          {
            from: 'assets/js',
            to: 'js'
          },
          {
            from: 'assets/favicon.ico',
            to: 'favicon.ico'
          },
          {
            from: 'frontend_event/google62f33f95b3037e0b.html',
            to: 'google62f33f95b3037e0b.html'
          },
          {
            from: 'frontend_event/bsqprevqual.html',
            to: 'bsqprevqual.html'
          },
          {
            from: 'frontend_event/db100final.html',
            to: 'db100final.html'
          },
          // Copy all HTML templates
          {
            from: 'frontend_event/**/*.html',
            to: '[path][name][ext]',
            context: 'frontend_event',
            globOptions: {
              ignore: ['**/index.html']
            }
          }
        ]
      })
    ],
    
    devServer: {
      static: {
        directory: path.join(__dirname, '.tmp/public_events'),
      },
      port: 3002,
      hot: true,
      historyApiFallback: true,
      proxy: [
        {
          context: ['/api'],
          target: 'http://localhost:3000',
          changeOrigin: true,
          secure: false
        }
      ]
    }
  });
};
